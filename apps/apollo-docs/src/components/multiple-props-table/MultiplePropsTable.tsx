import React, { useState, useEffect } from "react"
import classNames from "classnames"
import styles from "./multiple-props-table.module.css"

interface PropData {
  name: string
  description: string | React.ReactNode
  defaultValue: string | undefined
  type?: string
}

interface TabData {
  label: string
  props: PropData[]
}

interface MultiplePropsTableProps {
  tabs: TabData[]
  defaultActiveTab?: number
}

const MultiplePropsTable: React.FC<MultiplePropsTableProps> = ({
  tabs = [],
  defaultActiveTab = 0,
}) => {
  const [activeTab, setActiveTab] = useState(defaultActiveTab)

  // Ensure activeTab is valid when tabs change
  useEffect(() => {
    if (tabs.length > 0 && (activeTab >= tabs.length || activeTab < 0)) {
      setActiveTab(0)
    }
  }, [tabs, activeTab])

  // Safety checks
  if (!tabs || tabs.length === 0) {
    return (
      <div className={styles.container}>
        <div className={styles.emptyState}>
          No tabs provided
        </div>
      </div>
    )
  }

  const currentTab = tabs[activeTab]
  if (!currentTab) {
    return (
      <div className={styles.container}>
        <div className={styles.errorState}>
          Invalid tab index: {activeTab}
        </div>
      </div>
    )
  }

  return (
    <div className={styles.container}>
        {/* Tabs Header */}
        <div
          className={styles.tabsHeader}
          role="tablist"
          aria-label="Component documentation tabs"
        >
        {tabs.map((tab, index) => (
          <button
            key={tab.label}
            className={classNames(styles.tab, {
              [styles.tabActive]: activeTab === index,
            })}
            onClick={() => setActiveTab(index)}
            onKeyDown={(e) => {
              if (e.key === "ArrowLeft" && index > 0) {
                setActiveTab(index - 1)
              } else if (e.key === "ArrowRight" && index < tabs.length - 1) {
                setActiveTab(index + 1)
              }
            }}
            type="button"
            aria-selected={activeTab === index}
            aria-controls={`tabpanel-${index}`}
            id={`tab-${index}`}
            role="tab"
            tabIndex={activeTab === index ? 0 : -1}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Table Content */}
      <div
        className={styles.tabpanel}
        role="tabpanel"
        id={`tabpanel-${activeTab}`}
        aria-labelledby={`tab-${activeTab}`}
      >
        <table className={styles.table}>
          <thead className={styles.tableHeader}>
            <tr>
              <th className={classNames(styles.th, styles.colName)}>
                Name
              </th>
              <th className={classNames(styles.th, styles.colDescription)}>
                Description
              </th>
              <th className={classNames(styles.th, styles.colDefault, styles.thLast)}>
                Default
              </th>
            </tr>
          </thead>
          <tbody className={styles.tableBody}>
            {(currentTab.props || []).map((prop, index) => (
              <tr key={prop.name || `prop-${index}`}>
                <td className={classNames(styles.td, styles.nameCell, styles.colName, styles.tdFirst)}>
                  {prop.name || "unnamed"}
                </td>
                <td className={classNames(styles.td, styles.descriptionCell, styles.colDescription)}>
                  <div>
                    {prop.description || "No description provided"}
                    {prop.type && (
                      <div>
                        {prop.type.includes("|") ? (
                          <div>
                            {prop.type.split("|").map((type, index) => (
                              <code key={index} className={styles.typeCode}>
                                {type.trim()}
                              </code>
                            ))}
                          </div>
                        ) : (
                          <code className={styles.typeCode}>{prop.type}</code>
                          )}
                      </div>
                    )}
                  </div>
                </td>
                <td className={classNames(styles.td, styles.defaultCell, styles.colDefault, styles.tdLast)}>
                  {prop.defaultValue && prop.defaultValue !== '-' ? <code className={styles.typeCode}>{prop.defaultValue}</code> : "-"}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default MultiplePropsTable