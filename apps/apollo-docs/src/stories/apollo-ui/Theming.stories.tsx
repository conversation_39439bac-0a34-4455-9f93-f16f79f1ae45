import { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { Button, createThemeV2, Theme, Typography } from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * Theme component
 *
 * The Theme component allows you to customize Apollo design tokens to satisfy UI diversity
 * from business or brand requirements, including color, typography, space, etc.
 *
 * In version 2.0, we provide enhanced theming capabilities including:
 * - Color mode theme (light/dark)
 * - Multiple themes
 * - Nested theme support
 * - Design token customization at Global, Base, and Alias levels
 */
const meta: Meta<typeof Theme> = {
  title: "@apollo∕ui/Theming/Theme",
  component: Theme,
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ padding: "20px" }}>
        <Story />
      </div>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "The Theme component provides a powerful theming system for Apollo UI components. It supports nested themes, color modes, and comprehensive design token customization.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { Theme, createThemeV2 } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="theme-props">Props</h2>
          <ArgTypes />
          <h2 id="theme-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use Theme component for version 2.0 theming with enhanced capabilities",
              "Prefer semantic token organization: Global → Base → Alias",
              "Use nested themes for component-specific customizations",
              "Implement proper color mode switching for accessibility",
              "Test theme changes across all components in your application",
              "Use createThemeV2() for type-safe theme configuration",
              "Avoid deep nesting of themes to maintain performance",
              "Document custom tokens for team consistency",
            ]}
          />
          <h2 id="theme-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              "Provide smooth transitions between light and dark modes to avoid jarring visual changes for users with vestibular disorders.",
              "Test your custom themes with screen readers to ensure text remains readable and semantic meaning is preserved.",
              <>
                Use the <code>mode</code> prop to respect user's system
                preferences for color scheme when possible.
              </>,
              "Avoid relying solely on color to convey information - use additional visual cues like icons or typography weight.",
            ]}
          />
          <h2 id="theme-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Theme component generates CSS custom properties (variables) that can be used for styling. These variables follow the Apollo design system naming conventions and provide access to all design tokens."
            data={[
              {
                cssClassName: "--apl-color-*",
                description:
                  "Global color tokens generated from theme configuration",
                usageNotes:
                  "Use for custom component styling with theme-aware colors",
              },
              {
                cssClassName: "--apl-base-*",
                description: "Base tokens derived from global tokens",
                usageNotes: "Intermediate tokens that reference global tokens",
              },
              {
                cssClassName: "--apl-alias-*",
                description: "Semantic alias tokens for component styling",
                usageNotes: "Preferred tokens for component development",
              },
            ]}
          />
          <h2 id="theme-type-definitions">Type Definitions</h2>
          <h3>ThemeConfig</h3>
          <p>
            The <code>ThemeConfig</code> interface defines the structure for theme configuration objects used with the Theme component.
          </p>
          <Source
            code={`interface ThemeConfig {
  /**
   * Optional design tokens configuration
   * Allows partial customization of any token in the design system
   */
  tokens?: Partial<ApolloDefineTokenConfig>

  /**
   * Whether this theme should inherit from parent themes
   * @default true
   */
  inherit?: boolean
}`}
            language="typescript"
          />

          <h3>ApolloDefineToken</h3>
          <p>
            The <code>ApolloDefineToken</code> type defines the complete structure of all available design tokens in the Apollo design system.
            It includes global tokens, base tokens, and alias tokens organized in a hierarchical structure.
          </p>
          <Source
            code={`type ApolloDefineToken = {
  /**
   * Global color tokens - Raw color values organized by color families
   * These are the foundation colors that other tokens reference
   */
  color: {
    "green-pine": {
      "0": string    // #000000
      "10": string   // #002109
      "20": string   // #003915
      // ... up to "100": string
    }
    "gray-smoke": {
      "0": string    // #000000
      "10": string   // #1C1B1C
      // ... color scale values
    }
    "blue-ocean": { /* ... */ }
    "gray-bluish": { /* ... */ }
    "red-cherry": { /* ... */ }
    "yellow-peanut": { /* ... */ }
    // ... other color families
  }

  /**
   * Global spacing tokens - Consistent spacing values
   */
  spacing: {
    "1": string     // 1px
    "2": string     // 2px
    "4": string     // 4px
    // ... up to "128": string
    "none": string  // 0px
    "reverse-2": string  // -2px
    // ... reverse values
  }

  /**
   * Global typography tokens - Font properties
   */
  typography: {
    "font-family": {
      "ibm-plex-sans-thai": string
    }
    "font-size": {
      "4": string   // 4px
      "6": string   // 6px
      // ... size scale
    }
    "font-weight": {
      "thin": number      // 100
      "light": number     // 300
      "regular": number   // 400
      "medium": number    // 500
      "semi-bold": number // 600
      "bold": number      // 700
      "black": number     // 900
    }
    "line-height": { /* ... */ }
    "font-spacing": { /* ... */ }
  }

  /**
   * Global elevation tokens - Shadow and blur effects
   */
  elevation: {
    "x-axis": { "1": string, "2": string, /* ... */ }
    "y-axis": { "1": string, "2": string, /* ... */ }
    "spread": { "1": string, "2": string, /* ... */ }
    "blur": { "2": string, "4": string, /* ... */ }
  }

  /**
   * Base tokens - Semantic mappings of global tokens
   * These reference global tokens and provide semantic meaning
   */
  base: {
    color: {
      primary: {
        "0": string   // "{color.green-pine.0}"
        "10": string  // "{color.green-pine.10}"
        // ... references to global color tokens
      }
      secondary: { /* ... */ }
      tertiary: { /* ... */ }
      neutral: { /* ... */ }
      danger: { /* ... */ }
      warning: { /* ... */ }
      success: { /* ... */ }
      // ... other semantic color groups
    }
    typography: {
      "font-family": { /* ... */ }
      "font-size": { /* ... */ }
      "font-weight": { /* ... */ }
      "line-height": { /* ... */ }
    }
    spacing: { /* ... */ }
    elevation: { /* ... */ }
  }

  /**
   * Alias tokens - Component-specific tokens for light/dark modes
   * These are the tokens components actually use
   */
  alias: {
    color: {
      light: {
        primary: {
          primary: string                    // "{base.color.primary.40}"
          "surface-tint": string            // "{base.color.primary.40}"
          "on-primary": string              // "{base.color.primary.100}"
          "primary-container": string       // "{base.color.primary.95}"
          hovered: string                   // "{base.color.primary.50}"
          focused: string                   // "{base.color.primary.60}"
          pressed: string                   // "{base.color.primary.30}"
          "text-only-background-hovered": string
        }
        error: { /* ... */ }
        warning: { /* ... */ }
        secondary: { /* ... */ }
        tertiary: { /* ... */ }
        "background-and-surface": { /* ... */ }
        "outline-and-border": { /* ... */ }
        effects: { /* ... */ }
        success: { /* ... */ }
        static: { /* ... */ }
      }
      dark: {
        // Same structure as light mode but with different token references
        primary: { /* ... */ }
        error: { /* ... */ }
        // ... other color groups
      }
    }
    typography: {
      "font-family": string
      display: {
        large: {
          "font-family": string
          "font-weight": string
          "font-size": string
          "line-height": string
        }
        medium: { /* ... */ }
        small: { /* ... */ }
      }
      headline: { /* ... */ }
      title: { /* ... */ }
      body: { /* ... */ }
      label: { /* ... */ }
    }
    spacing: {
      margin: { /* ... */ }
      padding: { /* ... */ }
      gap: { /* ... */ }
    }
    elevation: { /* ... */ }
  }
}`}
            language="typescript"
          />

          <h3>Usage Examples</h3>
          <p>Here are examples of how to use these types when creating custom themes:</p>
          <Source
            code={`import { createThemeV2, type ThemeConfig, type ApolloDefineToken } from "@apollo/ui"

// Example 1: Basic theme configuration
const customTheme: ThemeConfig = {
  tokens: {
    // Override global color tokens
    color: {
      "brand-primary": {
        40: "#007bff",
        50: "#0056b3",
      }
    },
    // Override base tokens to use custom colors
    base: {
      color: {
        primary: {
          40: "{color.brand-primary.40}",
          50: "{color.brand-primary.50}",
        }
      }
    }
  },
  inherit: true
}

// Example 2: Typography-focused theme
const typographyTheme: ThemeConfig = {
  tokens: {
    typography: {
      "font-family": {
        "custom-font": "Inter, system-ui, sans-serif"
      }
    },
    base: {
      typography: {
        "font-family": {
          primary: "{typography.font-family.custom-font}"
        }
      }
    }
  }
}

// Example 3: Using the theme with createThemeV2
const theme = createThemeV2(customTheme)`}
            language="typescript"
          />

          <h2 id="theme-examples">Examples</h2>
          <Stories title="" />
          <h2 id="theme-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <Theme
                      theme={createThemeV2({
                        tokens: {
                          color: {
                            "brand-primary": {
                              40: "#007bff",
                            },
                          },
                          base: {
                            color: {
                              primary: {
                                40: "{color.brand-primary.40}",
                              },
                            },
                          },
                        },
                      })}
                    >
                      <Button>Semantic Token Usage</Button>
                    </Theme>
                  ),
                  description:
                    "Use semantic token organization with clear naming conventions",
                },
                negative: {
                  component: (
                    <Theme
                      theme={createThemeV2({
                        tokens: {
                          alias: {
                            color: {
                              light: {
                                primary: {
                                  primary: "#007bff",
                                },
                              },
                            },
                          },
                        },
                      })}
                    >
                      <Button>Direct Color Values</Button>
                    </Theme>
                  ),
                  description:
                    "Don't use direct color values in alias tokens - use references to maintain consistency",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    theme: {
      description: "Theme configuration object containing tokens and settings",
      control: { type: "object" },
      table: {
        type: { summary: "ThemeConfig" },
      },
    },
    mode: {
      description: "Color mode for the theme",
      control: { type: "select" },
      options: ["light", "dark"],
      table: {
        type: { summary: '"light" | "dark"' },
        defaultValue: { summary: '"light"' },
      },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Basic usage story
export const Default: Story = {
  args: {
    children: <Button>Default Theme</Button>,
  },
  render: (args) => <Theme {...args} />,
}

// Custom design tokens story
export const CustomDesignTokens: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
      <Theme>
        <Button>Default</Button>
      </Theme>
      <Theme
        theme={createThemeV2({
          tokens: {
            // Global Token
            color: {
              "green-pine": {
                0: "#000000",
                40: "#7FDA8E",
                50: "#9BF7A7",
                70: "#F6FFF2",
                100: "#FFFFFF",
              },
            },
            // Base Token
            base: {
              color: {
                primary: {
                  0: "{color.green-pine.0}",
                  40: "{color.green-pine.40}",
                  70: "{color.green-pine.70}",
                  100: "{color.green-pine.100}",
                },
              },
            },
            // Alias Token
            alias: {
              color: {
                light: {
                  primary: {
                    primary: "{base.color.primary.40}",
                    "on-primary": "{base.color.primary.0}",
                  },
                },
                dark: {
                  primary: {
                    primary: "{base.color.primary.70}",
                    "on-primary": "{base.color.primary.100}",
                  },
                },
              },
            },
          },
        })}
      >
        <Button>Custom Tokens</Button>
      </Theme>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates how to customize design tokens at Global, Base, and Alias levels. This example shows how to override the primary color system with custom green tokens.",
      },
    },
  },
}


export const ColorMode: Story = {
  render: () => {
  const [colorMode, setColorMode] = useState<"light" | "dark">("light")

  return (
    <div
      style={{
        display: "flex",
        gap: "16px",
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Theme mode={colorMode}>
        <Button>{colorMode === "light" ? "Light Mode" : "Dark Mode"}</Button>
      </Theme>
      <Theme
        theme={createThemeV2({
          tokens: {
            alias: {
              color: {
                light: {
                  primary: {
                    primary: "{base.color.primary.40}",
                    "on-primary": "{base.color.primary.100}",
                  },
                },
                dark: {
                  primary: {
                    primary: "{base.color.primary.40}",
                    "on-primary": "{base.color.primary.100}",
                  },
                },
              },
            },
          },
        })}
        mode={colorMode}
      >
        <Button>Light Mode Always</Button>
      </Theme>
      <Theme mode="dark">
        <Button>Dark Mode Always</Button>
      </Theme>
      <Theme mode={colorMode}>
        <Button
          onClick={() => setColorMode(colorMode === "light" ? "dark" : "light")}
        >
          Toggle Mode
        </Button>
      </Theme>
    </div>
  )
},
  parameters: {
    docs: {
      description: {
        story:
          "Shows how to use the mode property to switch between light and dark themes. You can set specific modes or create interactive toggles.",
      },
    },
  },
}

// Nested themes story
export const NestedThemes: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        gap: "16px",
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Theme mode="dark">
        <div
          style={{
            padding: "16px",
            border: "1px solid #ccc",
            borderRadius: "8px",
          }}
        >
          <Typography level="bodyMedium" style={{ marginBottom: "8px" }}>
            Dark Theme Parent
          </Typography>
          <Button style={{ marginRight: "8px" }}>Hello Dark</Button>
          <Theme
            theme={createThemeV2({
              tokens: {
                base: {
                  color: {
                    primary: {
                      0: "{color.gray-bluish.0}",
                      10: "{color.gray-bluish.10}",
                      20: "{color.gray-bluish.20}",
                      30: "{color.gray-bluish.30}",
                      40: "#565F71",
                      50: "{color.gray-bluish.50}",
                      60: "{color.gray-bluish.60}",
                      70: "{color.gray-bluish.70}",
                      80: "{color.gray-bluish.80}",
                      90: "{color.gray-bluish.90}",
                      95: "#ECF0FF",
                      99: "#FDFBFF",
                      100: "{color.gray-bluish.100}",
                    },
                    secondary: {
                      0: "{color.green-pine.0}",
                      10: "{color.green-pine.10}",
                      20: "{color.green-pine.20}",
                      30: "{color.green-pine.30}",
                      40: "{color.green-pine.40}",
                      50: "{color.green-pine.50}",
                      60: "{color.green-pine.60}",
                      70: "{color.green-pine.70}",
                      80: "{color.green-pine.80}",
                      90: "{color.green-pine.90}",
                      99: "{color.green-pine.99}",
                      95: "{color.green-pine.95}",
                      100: "{color.green-pine.100}",
                    },
                  },
                },
              },
            })}
            mode="light"
          >
            <div
              style={{
                padding: "8px",
                border: "1px solid #eee",
                borderRadius: "4px",
                marginTop: "8px",
              }}
            >
              <Typography level="bodySmall" style={{ marginBottom: "8px" }}>
                Light Theme Child with Custom Colors
              </Typography>
              <Button style={{ marginRight: "8px" }}>Hello Light</Button>
              <Theme>
                <Button>Hello Inherit</Button>
              </Theme>
            </div>
          </Theme>
        </div>
      </Theme>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates nested theme support. Child themes can inherit from parent themes or override specific properties. Themes that don't specify a mode will inherit from their parent.",
      },
    },
  },
}


export const ThemeSwitching: Story = {
  render: () => {
  const [currentTheme, setCurrentTheme] = useState<
    "default" | "brand" | "high-contrast"
  >("default")

  const themes = {
    default: createThemeV2({}),
    brand: createThemeV2({
      tokens: {
        color: {
          "brand-blue": {
            40: "#1e40af",
            50: "#3b82f6",
            60: "#60a5fa",
          },
        },
        base: {
          color: {
            primary: {
              40: "{color.brand-blue.40}",
              50: "{color.brand-blue.50}",
              60: "{color.brand-blue.60}",
            },
          },
        },
      },
    }),
    "high-contrast": createThemeV2({
      tokens: {
        color: {
          "high-contrast": {
            0: "#000000",
            100: "#ffffff",
          },
        },
        base: {
          color: {
            primary: {
              0: "{color.high-contrast.0}",
              100: "{color.high-contrast.100}",
            },
          },
        },
        alias: {
          color: {
            light: {
              primary: {
                primary: "{base.color.primary.0}",
                "on-primary": "{base.color.primary.100}",
              },
            },
          },
        },
      },
    }),
  }

  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
      <div style={{ display: "flex", gap: "8px", marginBottom: "16px" }}>
        <Button
          variant={currentTheme === "default" ? "filled" : "outline"}
          onClick={() => setCurrentTheme("default")}
        >
          Default
        </Button>
        <Button
          variant={currentTheme === "brand" ? "filled" : "outline"}
          onClick={() => setCurrentTheme("brand")}
        >
          Brand
        </Button>
        <Button
          variant={currentTheme === "high-contrast" ? "filled" : "outline"}
          onClick={() => setCurrentTheme("high-contrast")}
        >
          High Contrast
        </Button>
      </div>

      <Theme theme={themes[currentTheme]}>
        <div
          style={{
            padding: "16px",
            border: "1px solid #ccc",
            borderRadius: "8px",
            display: "flex",
            gap: "12px",
            alignItems: "center",
            flexWrap: "wrap",
          }}
        >
          <Button>Primary Action</Button>
          <Button variant="outline">Secondary Action</Button>
          <Typography level="bodyMedium">
            Current theme: {currentTheme}
          </Typography>
        </div>
      </Theme>
    </div>
  )
},
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates dynamic theme switching in applications. This pattern is useful for allowing users to choose between different visual themes or accessibility modes.",
      },
    },
  },
}

// Component-specific theme overrides
export const ComponentSpecificOverrides: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
      <div>
        <Typography level="titleMedium" style={{ marginBottom: "12px" }}>
          Global Theme
        </Typography>
        <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
          <Button>Global Button</Button>
          <Typography level="bodyMedium">Uses global theme tokens</Typography>
        </div>
      </div>

      <div>
        <Typography level="titleMedium" style={{ marginBottom: "12px" }}>
          Component-Specific Override
        </Typography>
        <Theme
          theme={createThemeV2({
            tokens: {
              alias: {
                color: {
                  light: {
                    primary: {
                      primary: "#dc2626", // Red override for this section
                      "on-primary": "#ffffff",
                    },
                  },
                },
              },
            },
          })}
        >
          <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
            <Button>Override Button</Button>
            <Typography level="bodyMedium">
              Uses component-specific red theme
            </Typography>
          </div>
        </Theme>
      </div>

      <div>
        <Typography level="titleMedium" style={{ marginBottom: "12px" }}>
          Nested Override with Inheritance
        </Typography>
        <Theme
          theme={createThemeV2({
            tokens: {
              color: {
                "custom-green": {
                  40: "#16a34a",
                  50: "#22c55e",
                },
              },
              base: {
                color: {
                  primary: {
                    40: "{color.custom-green.40}",
                    50: "{color.custom-green.50}",
                  },
                },
              },
            },
          })}
        >
          <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
            <Button>Green Theme</Button>
            <Theme theme={createThemeV2({ tokens: {}, inherit: false })}>
              <Button>Non-inherited Default</Button>
            </Theme>
          </div>
        </Theme>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Shows how to apply theme overrides to specific components or sections while maintaining global theme consistency. Useful for creating distinct visual areas within an application.",
      },
    },
  },
}

// Theme composition and merging
export const ThemeComposition: Story = {
  render: () => {
    // Base brand theme
    const brandTheme = createThemeV2({
      tokens: {
        color: {
          "brand-primary": {
            40: "#6366f1",
            50: "#8b5cf6",
            60: "#a855f7",
          },
          "brand-secondary": {
            40: "#06b6d4",
            50: "#0891b2",
          },
        },
        base: {
          color: {
            primary: {
              40: "{color.brand-primary.40}",
              50: "{color.brand-primary.50}",
              60: "{color.brand-primary.60}",
            },
            secondary: {
              40: "{color.brand-secondary.40}",
              50: "{color.brand-secondary.50}",
            },
          },
        },
      },
    })

    // Typography enhancement theme
    const typographyTheme = createThemeV2({
      tokens: {
        typography: {
          "font-family": {
            primary: "Inter, system-ui, sans-serif",
          },
          "font-size": {
            "body-large": "18px",
            "body-medium": "16px",
          },
        },
        base: {
          typography: {
            "font-family": {
              primary: "{typography.font-family.primary}",
            },
            "font-size": {
              "body-large": "{typography.font-size.body-large}",
              "body-medium": "{typography.font-size.body-medium}",
            },
          },
        },
      },
    })

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
        <div>
          <Typography level="titleMedium" style={{ marginBottom: "12px" }}>
            Brand Theme Only
          </Typography>
          <Theme theme={brandTheme}>
            <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
              <Button>Primary</Button>
              <Button variant="outline">Secondary</Button>
              <Typography level="bodyMedium">Brand colors applied</Typography>
            </div>
          </Theme>
        </div>

        <div>
          <Typography level="titleMedium" style={{ marginBottom: "12px" }}>
            Composed Theme (Brand + Typography)
          </Typography>
          <Theme theme={brandTheme}>
            <Theme theme={typographyTheme}>
              <div
                style={{ display: "flex", gap: "12px", alignItems: "center" }}
              >
                <Button>Primary</Button>
                <Button variant="outline">Secondary</Button>
                <Typography level="bodyMedium">
                  Brand colors + enhanced typography
                </Typography>
              </div>
            </Theme>
          </Theme>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates theme composition by layering multiple theme configurations. This approach allows for modular theme development and easier maintenance of complex design systems.",
      },
    },
  },
}