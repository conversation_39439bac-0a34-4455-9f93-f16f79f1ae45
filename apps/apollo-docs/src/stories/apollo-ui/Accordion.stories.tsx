import { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { Accordion, Button, Typography } from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

// Metadata for the Storybook component
const meta: Meta<typeof Accordion> = {
  title: "@apollo∕ui/Components/Data Display/Accordion",
  component: Accordion,
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2981-19891&m=dev",
    },
    docs: {
      description: {
        component:
          "The Accordion component allows users to toggle the display of sections of content. It's useful for organizing information in a compact, scannable format.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { Accordion } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="accordion-props">Props</h2>
          <ArgTypes />
          <h2 id="accordion-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use Accordion to organize related content that can be grouped into logical sections",
              "Keep accordion labels clear and descriptive to indicate the content within",
              "Limit the number of accordion items to avoid overwhelming users (typically 3-7 items)",
              "Consider the content hierarchy - use accordions for secondary information that doesn't need to be immediately visible",
              "Ensure accordion content is self-contained and doesn't require context from other sections",
              "Use the danger variant sparingly, only for critical or error-related content",
            ]}
          />
          <h2 id="accordion-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              "Screen readers announce the accordion state (expanded/collapsed) automatically.",
              <>
                Provide descriptive <code>label</code> content that clearly
                indicates what information the accordion contains.
              </>,
              "Focus management is handled automatically when toggling accordion sections.",
              <>
                Use <code>hasDivider</code> prop to display a divider line
                bottom of the accordion
              </>,
            ]}
          />
          <h2 id="accordion-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Accordion component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloAccordion-container",
                description:
                  "Styles applied to the accordion container element",
                usageNotes:
                  "Use for overall accordion styling including border and background",
              },
              {
                cssClassName: ".ApolloAccordion-trigger",
                description: "Styles applied to the accordion trigger button",
                usageNotes:
                  "Contains trigger styling including hover and disabled states",
              },
              {
                cssClassName: ".ApolloAccordion-trigger-content",
                description: "Styles applied to the trigger content wrapper",
                usageNotes: "Contains the accordion label and content styling",
              },
              {
                cssClassName: ".ApolloAccordion-panel",
                description: "Styles applied to the collapsible panel",
                usageNotes: "Contains panel animation and overflow properties",
              },
              {
                cssClassName: ".ApolloAccordion-divider",
                description: "Styles applied to the divider element",
                usageNotes:
                  "Used when hasDivider=true to separate accordion sections",
              },
            ]}
          />
          <h2 id="accordion-examples">Examples</h2>
          <Stories title="" />
          <h2 id="accordion-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ width: "400px" }}>
                      <Accordion
                        label="Account Settings"
                        defaultOpen={false}
                        fullWidth
                      >
                        <Typography level="bodyLarge">
                          Configure your account preferences, notification
                          settings, and privacy options.
                        </Typography>
                      </Accordion>
                    </div>
                  ),
                  description:
                    "Use clear, descriptive labels that indicate the content within each accordion section",
                },
                negative: {
                  component: (
                    <div style={{ width: "400px" }}>
                      <Accordion
                        label="Section 1"
                        defaultOpen={false}
                        fullWidth
                      >
                        <Typography level="bodyLarge">
                          Some content that users can't identify from the label.
                        </Typography>
                      </Accordion>
                    </div>
                  ),
                  description:
                    "Avoid generic labels that don't provide context about the accordion content",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ width: "400px" }}>
                      <Accordion
                        label="System Error Details"
                        variant="danger"
                        defaultOpen={false}
                        fullWidth
                      >
                        <Typography level="bodyLarge">
                          Critical system error occurred. Please contact support
                          immediately.
                        </Typography>
                      </Accordion>
                    </div>
                  ),
                  description:
                    "Use the danger variant appropriately for critical or error-related content that requires attention",
                },
                negative: {
                  component: (
                    <div style={{ width: "400px" }}>
                      <Accordion
                        label="General Information"
                        variant="danger"
                        defaultOpen={false}
                        fullWidth
                      >
                        <Typography level="bodyLarge">
                          This is just regular information about our services
                          and features.
                        </Typography>
                      </Accordion>
                    </div>
                  ),
                  description:
                    "Don't use the danger variant for regular content - reserve it for actual errors or critical information",
                },
              },
              {
                positive: {
                  component: (
                    <div
                      style={{
                        width: "400px",
                        display: "flex",
                        flexDirection: "column",
                        gap: "8px",
                      }}
                    >
                      <Accordion
                        label="Personal Information"
                        defaultOpen={false}
                        fullWidth
                      >
                        <Typography level="bodyLarge">
                          Update your name, email, and contact details.
                        </Typography>
                      </Accordion>
                      <Accordion
                        label="Security Settings"
                        defaultOpen={false}
                        fullWidth
                      >
                        <Typography level="bodyLarge">
                          Manage passwords and two-factor authentication.
                        </Typography>
                      </Accordion>
                      <Accordion
                        label="Billing Information"
                        defaultOpen={false}
                        fullWidth
                      >
                        <Typography level="bodyLarge">
                          View payment methods and billing history.
                        </Typography>
                      </Accordion>
                    </div>
                  ),
                  description:
                    "Group related content logically and keep the number of accordion items manageable (3-7 items)",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        width: "400px",
                        display: "flex",
                        flexDirection: "column",
                        gap: "4px",
                        padding: 16,
                      }}
                    >
                      {Array.from({ length: 10 }, (_, i) => (
                        <Accordion
                          key={i}
                          label={`Item ${i + 1}`}
                          defaultOpen={false}
                          fullWidth
                          borderless
                        >
                          <Typography level="bodySmall">
                            Content for item {i + 1}
                          </Typography>
                        </Accordion>
                      ))}
                    </div>
                  ),
                  description:
                    "Don't overwhelm users with too many accordion items - it becomes difficult to scan and navigate",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  tags: ["autodocs"],
  argTypes: {
    label: {
      control: "text",
      description: "Content for the accordion header.",
      table: { type: { summary: "ReactNode" } },
    },
    open: {
      control: "boolean",
      description: "Controls the open state when component is controlled.",
      table: { type: { summary: "boolean" } },
    },
    defaultOpen: {
      control: "boolean",
      description: "Initial open state for uncontrolled component.",
      table: { type: { summary: "boolean" } },
    },
    onOpenChange: {
      action: "onOpenChange",
      description: "Callback when open state changes.",
      table: { type: { summary: "(open: boolean) => void" } },
    },
    iconPosition: {
      control: { type: "radio" },
      options: ["start", "end", "both"],
      description: "Position of the chevron icon.",
      table: { type: { summary: '"start" | "end" | "both"' } },
    },
    borderless: {
      control: "boolean",
      description: "Removes the border styling.",
      table: { type: { summary: "boolean" } },
    },
    variant: {
      control: { type: "radio" },
      options: ["default", "danger"],
      description: "Visual variant of the accordion.",
      table: { type: { summary: '"default" | "danger"' } },
    },
    iconVariant: {
      control: { type: "radio" },
      options: ["default", "primary"],
      description: "Visual variant of the icon.",
      table: {
        type: { summary: '"default" | "primary"' },
        defaultValue: { summary: "default" },
      },
    },
    fullWidth: {
      control: "boolean",
      description: "Whether accordion takes full width of container.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    keepMounted: {
      control: "boolean",
      description: "Keep contents mounted when closed.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    disabled: {
      control: "boolean",
      description: "Disables the accordion.",
      table: { type: { summary: "boolean" } },
    },
    hasDivider: {
      control: "boolean",
      description: "Adds a divider line below the accordion header.",
      table: { type: { summary: "boolean" } },
    },
    className: {
      control: "text",
      description: "Additional CSS class names for the root element.",
      table: { type: { summary: "string" } },
    },
    ref: {
      control: false,
      description: "Ref for the root element.",
      table: { type: { summary: "Ref<HTMLDivElement>" } },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Basic Accordion Story
export const Basic: Story = {
  args: {
    label: "Basic Accordion",
    children: (
      <Typography level="bodyLarge">
        This is the content of the basic accordion.
      </Typography>
    ),
    defaultOpen: false,
  },
  render: (args) => (
    <div style={{ width: "600px" }}>
      <Accordion {...args}>{args.children}</Accordion>
    </div>
  ),
}

// Dnager Variant Story
export const Danger: Story = {
  args: {
    label: "Error Variant",
    children: (
      <Typography level="bodyLarge">
        This is the content of the accordion.
      </Typography>
    ),
    variant: "danger",
    fullWidth: true,
  },
  render: (args) => (
    <div style={{ width: "600px" }}>
      <Accordion {...args}>{args.children}</Accordion>
    </div>
  ),
}

// Icon Positioning Story
export const IconPositioning: Story = {
  args: {
    label: "Icon Positioning",
    children: (
      <Typography level="bodyLarge">
        This is the content of the accordion.
      </Typography>
    ),
    fullWidth: true,
  },
  render: (args) => (
    <div style={{ width: "600px" }}>
      <Accordion {...args} iconPosition="start">
        {args.children}
      </Accordion>
    </div>
  ),
}

// Has Divider Accordion Story
export const WithDivider: Story = {
  args: {
    label: "With Divider",
    children: (
      <Typography level="bodyLarge">
        This is the content of the accordion. It is hidden by default and can be
        toggled by clicking the header.
      </Typography>
    ),
    hasDivider: true,
    fullWidth: true,
  },
  render: (args) => (
    <div style={{ width: "600px" }}>
      <Accordion {...args}>{args.children}</Accordion>
    </div>
  ),
}

// Disabled Accordion Story
export const Disabled: Story = {
  args: {
    label: "Disabled Accordion",
    children: (
      <Typography level="bodyLarge">
        You should not be able to see this content because the accordion is
        disabled.
      </Typography>
    ),
    disabled: true,
    fullWidth: true,
  },
  render: (args) => (
    <div style={{ width: "600px" }}>
      <Accordion {...args}>{args.children}</Accordion>
    </div>
  ),
}

// Controlled Accordion Story
export const Controlled: Story = {
  render: (args) => {
    const [isOpen, setIsOpen] = useState(false)
    return (
      <div style={{ width: "600px" }}>
        <Button
          onClick={() => setIsOpen(!isOpen)}
          style={{ marginBottom: "1rem", padding: "0.5rem 1rem" }}
        >
          Toggle Accordion
        </Button>
        <Accordion {...args} open={isOpen} onOpenChange={setIsOpen}>
          {args.children}
        </Accordion>
      </div>
    )
  },
  args: {
    label: "Controlled Accordion",
    children: (
      <Typography level="bodyLarge">
        This accordion's state is controlled by the button above. The
        `onOpenChange` callback updates the external state.
      </Typography>
    ),
    fullWidth: true,
  },
}

export const Complex: Story = {
  args: {
    label: (
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography level="labelMedium" style={{ maxWidth: "100px" }}>
          011111 ชื่อสาขาสามารถยาว ได้้สองบรรทัด (จังหวัด)
        </Typography>
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "flex-end",
            gap: "8px",
          }}
        >
          <Typography level="labelMedium">เปิดทำการ</Typography>
          <Button
            onClick={(e) => e.stopPropagation()}
            variant="outline"
            size="small"
          >
            เพิ่ม
          </Button>
        </div>
      </div>
    ),
    children: (
      <div style={{ display: "flex", flexDirection: "column" }}>
        <span>Model: CJ SUPERMARKET + BAO CAFÉ&WASH </span>
        <span>Open date: 30/08/2004 Close</span>
        <span>date: 30/08/2004</span>
      </div>
    ),
    defaultOpen: true,
    borderless: true,
    fullWidth: true,
    iconPosition: "start",
    iconVariant: "primary",
  },
  render: (args) => (
    <div style={{ width: "600px" }}>
      <Accordion {...args}>{args.children}</Accordion>
    </div>
  ),
}
